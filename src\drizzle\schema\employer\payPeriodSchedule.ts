import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

export const PAY_PERIOD_TYPES = [
  'weekly',
  'two_weekly',
  'four_weekly',
  'monthly',
  'quarterly',
  'yearly',
] as const;

export interface PayPeriodSchedule {
  id: string;
  label: string;
  type: string;
  created_at: number;
  updated_at: number;
}

export const payPeriodSchedules = sqliteTable(
  'pay_period_schedules',
  {
    id: text('id').primaryKey().notNull(),
    label: text('label').notNull(), // Custom label or period type fallback
    type: text('type', { enum: PAY_PERIOD_TYPES }).notNull(),
    created_at: integer('created_at').notNull().default(sql`(strftime('%s','now'))`),
    updated_at: integer('updated_at').notNull().default(sql`(strftime('%s','now'))`),
  }
);
