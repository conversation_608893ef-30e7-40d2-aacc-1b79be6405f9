CREATE TABLE IF NOT EXISTS `employee` (
	`id` text PRIMARY KEY NOT NULL,
	`title` text NOT NULL,
	`first_name` text NOT NULL,
	`middle_name` text,
	`last_name` text NOT NULL,
	`date_of_birth` text NOT NULL,
	`gender` text DEFAULT 'Male' NOT NULL,
	`nationality` text,
	`passport_number` text,
	`marital_status` text,
	`address1` text,
	`address2` text,
	`address3` text,
	`address4` text,
	`postcode` text,
	`country` text DEFAULT 'England' NOT NULL,
	`email` text,
	`email_type` text DEFAULT 'Personal',
	`phone` text,
	`phone_type` text DEFAULT 'Mobile',
	`additional_emails` text,
	`additional_phones` text,
	`emergency_contacts` text,
	`works_number` text,
	`start_date` text NOT NULL,
	`departments` text,
	`job_title` text,
	`is_protected_under_tupe` integer DEFAULT false,
	`leave_date` text,
	`payroll_id` text,
	`change_of_payroll_id` text,
	`contracted_hours` text,
	`irregular_payment_pattern` integer DEFAULT false,
	`non_individual` integer DEFAULT false,
	`leave_year_starts` text,
	`annual_leave_calculation_method` text,
	`annual_leave_entitlement` integer,
	`annual_leave_carry_over` integer DEFAULT false,
	`annual_leave_carry_over_value` integer DEFAULT 0,
	`annual_leave_carry_over_unit` text DEFAULT 'days',
	`annual_leave_adjustment` integer DEFAULT false,
	`annual_leave_adjustment_value` integer DEFAULT 0,
	`annual_leave_adjustment_unit` text DEFAULT 'days',
	`working_days` text,
	`overseas_employer` integer DEFAULT false,
	`starter_declaration` text,
	`previous_employment_pay` real,
	`previous_employment_tax` real,
	`payment_frequency` text,
	`pay_calculation_method` text,
	`period_pay_rate` real,
	`annual_salary` real,
	`starting_salary` real,
	`hourly_rates` text,
	`daily_rates` text,
	`payment_method` text,
	`payment_details` text,
	`minimum_wage_profile` text,
	`typical_hours_worked` real,
	`zero_pay_handling` text,
	`tax_code` text,
	`week1_month1` integer DEFAULT false,
	`ni_table` text,
	`employer_ni_contributions` text,
	`ni_number` text,
	`is_director` integer DEFAULT false,
	`director_start_date` text,
	`director_end_date` text,
	`director_ni_calc_method` text DEFAULT 'standard',
	`is_off_payroll_worker` integer DEFAULT false,
	`trivial_commutation` integer DEFAULT false,
	`flexible_drawdown` integer DEFAULT false,
	`student_loan` text,
	`student_loan_start_date` text,
	`student_loan_end_date` text,
	`postgraduate_loan` text,
	`pg_loan_start_date` text,
	`pg_loan_end_date` text,
	`next_review_date` text,
	`medical` text,
	`notes` text,
	`is_confidential` integer DEFAULT false,
	`enable_self_service` integer DEFAULT false,
	`pdf_password` text
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS `employer` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`trading_name` text,
	`address1` text,
	`address2` text,
	`address3` text,
	`address4` text,
	`postcode` text,
	`country` text,
	`office_number` text,
	`paye_reference` text,
	`accounts_office_reference` text,
	`hmrc_office` text,
	`small_employers_relief` integer DEFAULT true,
	`company_registration_number` text,
	`unique_tax_reference` text,
	`corporation_tax_reference` text,
	`bacs_sun` text,
	`sender_type` text DEFAULT 'Employer',
	`sender_id` text,
	`password` text,
	`contact_title_rti` text DEFAULT 'Mr',
	`contact_first_name_rti` text,
	`contact_last_name_rti` text,
	`contact_email_rti` text,
	`contact_phone_rti` text,
	`contact_fax_rti` text,
	`typical_pay_frequency` text DEFAULT 'Monthly',
	`typical_pay_basis` text DEFAULT 'Salaried',
	`typical_pay_method` text DEFAULT 'DirectDeposit',
	`typical_leave_year_start` text DEFAULT 'January',
	`typical_leave_calculation_method` text DEFAULT 'Days',
	`typical_leave_entitlement` real DEFAULT 28,
	`typical_working_days` text,
	`typical_hours_worked` real,
	`typical_minimum_wage_profile` text DEFAULT 'Standard',
	`contact_name` text,
	`contact_email` text,
	`contact_phone` text,
	`alt_contact_name` text,
	`alt_contact_email` text,
	`alt_contact_phone` text,
	`filing_types` text,
	`client_notes` text
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS `pay_periods` (
	`id` text PRIMARY KEY NOT NULL,
	`schedule_id` text NOT NULL,
	`type` text NOT NULL,
	`start_day` integer NOT NULL,
	`end_day` integer NOT NULL,
	`pay_date_rule` text NOT NULL,
	`period_number` integer NOT NULL,
	`period_end` text NOT NULL,
	`active` integer DEFAULT true NOT NULL,
	`created_at` integer DEFAULT (strftime('%s','now')) NOT NULL,
	`updated_at` integer DEFAULT (strftime('%s','now')) NOT NULL,
	`notes` text,
	FOREIGN KEY (`schedule_id`) REFERENCES `pay_period_schedules`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS `pay_periods_type_idx` ON `pay_periods` (`type`);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS `pay_periods_active_idx` ON `pay_periods` (`active`);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS `pay_periods_created_at_idx` ON `pay_periods` (`created_at`);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS `pay_periods_updated_at_idx` ON `pay_periods` (`updated_at`);--> statement-breakpoint
CREATE TABLE IF NOT EXISTS `pay_period_schedules` (
	`id` text PRIMARY KEY NOT NULL,
	`label` text NOT NULL,
	`type` text NOT NULL,
	`created_at` integer DEFAULT (strftime('%s','now')) NOT NULL,
	`updated_at` integer DEFAULT (strftime('%s','now')) NOT NULL
);
