import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { PAY_PERIOD_TYPES } from "@/drizzle/schema/employer/payPeriod";
import {
  useCreatePayPeriodMutation,
  useCreatePayPeriodsMutation,
  useCreatePayPeriodScheduleAndPeriodsMutation,
} from "@/hooks/tanstack-query/usePayPeriods";
import { z } from "zod";

const WEEKDAYS = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];
const PERIOD_DATES = Array.from({ length: 28 }, (_, i) => (i + 1).toString());

interface PayrollWizardModalProps {
  open: boolean;
  payFrequency: (typeof PAY_PERIOD_TYPES)[number] | null;
  onClose: () => void;
}

export const PayrollWizardModal: React.FC<PayrollWizardModalProps> = ({
  open,
  payFrequency,
  onClose,
}) => {
  console.log("[PayrollWizardModal] render", { open, payFrequency });
  // --- Resettable state ---
  const [label, setLabel] = useState("");
  const [periodEndDay, setPeriodEndDay] = useState<string>("Friday");
  const [periodEndDate, setPeriodEndDate] = useState<string>("28");
  const [periodEndIsLast, setPeriodEndIsLast] = useState(false);
  const [payDateRuleType, setPayDateRuleType] = useState<string>("PED");
  const [payDateWeekday, setPayDateWeekday] = useState<string>("Friday");
  const [payDateWhichMonth, setPayDateWhichMonth] = useState<string>("this");
  const [payDateParticularDate, setPayDateParticularDate] =
    useState<string>("28");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset all wizard state
  function resetState() {
    setLabel("");
    setPeriodEndDay("Friday");
    setPeriodEndDate("28");
    setPeriodEndIsLast(false);
    setPayDateRuleType("PED");
    setPayDateWeekday("Friday");
    setPayDateWhichMonth("this");
    setPayDateParticularDate("28");
    setIsSubmitting(false);
    setError(null);
  }

  // Wrap onClose to always reset state first
  function handleClose() {
    console.log("[PayrollWizardModal] handleClose called");
    resetState();
    onClose();
  }

  const createSingleMutation = useCreatePayPeriodMutation();
  const createScheduleAndPeriodsMutation =
    useCreatePayPeriodScheduleAndPeriodsMutation();
  if (!open || !payFrequency) return null; // Already guarded in parent
  // Type assertion to ensure payFrequency is always a valid string below
  const safePayFrequency = payFrequency as (typeof PAY_PERIOD_TYPES)[number];

  // Build period end UI
  let periodEndInput = null;
  if (["weekly", "two_weekly", "four_weekly"].includes(payFrequency)) {
    periodEndInput = (
      <Select value={periodEndDay} onValueChange={setPeriodEndDay}>
        <SelectTrigger className="mb-2 w-full">
          <SelectValue placeholder="Select period end day" />
        </SelectTrigger>
        <SelectContent>
          {WEEKDAYS.map((d) => (
            <SelectItem key={d} value={d}>
              {d}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  } else {
    periodEndInput = (
      <div className="mb-2 flex items-center gap-2">
        <Select
          value={periodEndDate}
          onValueChange={(v) => {
            setPeriodEndDate(v);
            setPeriodEndIsLast(false);
          }}
        >
          <SelectTrigger className="w-32">
            <SelectValue placeholder="End date" />
          </SelectTrigger>
          <SelectContent>
            {PERIOD_DATES.map((d) => (
              <SelectItem key={d} value={d}>
                {d}
              </SelectItem>
            ))}
            <SelectItem value="last">Last day of month</SelectItem>
          </SelectContent>
        </Select>
      </div>
    );
  }

  // Build pay date rule UI
  let payDateRuleOptions = [
    { value: "PED", label: "Period End Date (PED)" },
    { value: "weekday_on_before_PED", label: "Weekday on or before PED" },
    { value: "weekday_on_after_PED", label: "Weekday on or after PED" },
  ];
  if (["weekly"].includes(payFrequency)) {
    payDateRuleOptions.push(
      { value: "weekday_after_PED", label: "Particular weekday after PED" },
      { value: "weekday_before_PED", label: "Particular weekday before PED" },
    );
  }
  if (["monthly", "quarterly", "yearly"].includes(payFrequency)) {
    payDateRuleOptions.push(
      {
        value: "particular_date_this_month",
        label: "Particular date (this month)",
      },
      {
        value: "particular_date_next_month",
        label: "Particular date (next month)",
      },
      { value: "last_working_day", label: "Last working day (Mon-Fri)" },
      { value: "last_day", label: "Last day of month" },
    );
  }

  // Helper function to format dates from ISO format (YYYY-MM-DD) to DD-MM-YYYY
  function formatDateToDDMMYYYY(isoDateString: string): string {
    const date = new Date(isoDateString);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  }

  // Build pay date rule JSON
  function buildPayDateRule() {
    switch (payDateRuleType) {
      case "PED":
        return { type: "PED" };
      case "weekday_on_before_PED":
      case "weekday_on_after_PED":
      case "weekday_after_PED":
      case "weekday_before_PED":
        return { type: payDateRuleType, weekday: payDateWeekday };
      case "particular_date_this_month":
      case "particular_date_next_month":
        return {
          type: payDateRuleType,
          date: payDateParticularDate,
          whichMonth: payDateWhichMonth,
        };
      case "last_working_day":
        return { type: "last_working_day" };
      case "last_day":
        return { type: "last_day" };
      default:
        return { type: payDateRuleType };
    }
  }

  // Helper: get the next period's start/end dates and pay date for each frequency
  function generatePayPeriodsForTaxYear({
    frequency,
    periodEndDay,
    periodEndDate,
    payDateRule,
  }: {
    frequency: string;
    periodEndDay: string;
    periodEndDate: string;
    payDateRule: any;
  }) {
    // Tax year runs from 6 April this year to 5 April next year
    const now = new Date();
    const currentYear =
      now.getMonth() >= 3 ? now.getFullYear() : now.getFullYear() - 1;
    const taxYearStart = new Date(currentYear, 3, 6); // 6 April
    const taxYearEnd = new Date(currentYear + 1, 3, 5); // 5 April next year
    const periods = [];
    let periodEnd: Date;
    let periodStart: Date;
    let numPeriods = 0;
    let periodLabel = "";
    let periodAdvanceDays = 0;
    let periodAdvanceMonths = 0; // Only one declaration of periodStart allowed
    switch (frequency) {
      case "monthly":
        numPeriods = 12;
        periodLabel = "Month";
        periodAdvanceMonths = 1;
        break;
      case "four_weekly":
        numPeriods = 13;
        periodLabel = "4-Week";
        periodAdvanceDays = 28;
        break;
      case "two_weekly":
        numPeriods = 26;
        periodLabel = "2-Week";
        periodAdvanceDays = 14;
        break;
      case "weekly":
        numPeriods = 53; // Will trim if pay date falls outside tax year
        periodLabel = "Week";
        periodAdvanceDays = 7;
        break;
      case "quarterly":
        numPeriods = 4;
        periodLabel = "Quarter";
        periodAdvanceMonths = 3;
        break;
      case "yearly":
        numPeriods = 1;
        periodLabel = "Year";
        periodAdvanceMonths = 12;
        break;
      default:
        break;
    }
    // Helper to get the next periodEnd date for each period
    function getNextPeriodEnd(prevEnd: Date, i: number): Date {
      if (["monthly", "quarterly", "yearly"].includes(frequency)) {
        // Use user-selected day or last day of month
        let d = new Date(prevEnd);
        d.setMonth(d.getMonth() + periodAdvanceMonths);
        if (frequency === "quarterly") d.setDate(0); // last day of prev month
        if (periodEndDate === "last") {
          d.setMonth(d.getMonth() + 1, 0); // last day of month
        } else {
          d.setDate(parseInt(periodEndDate, 10));
          // If date overflows, JS auto-fixes to next month, so clamp
          if (
            d.getMonth() !==
            (prevEnd.getMonth() + periodAdvanceMonths) % 12
          ) {
            d.setDate(0);
          }
        }
        return d;
      } else {
        // Weekly, 2-weekly, 4-weekly
        let d = new Date(prevEnd);
        d.setDate(d.getDate() + periodAdvanceDays);
        return d;
      }
    }
    // Calculate first period end and start for monthly/quarterly/yearly
    if (["monthly", "quarterly", "yearly"].includes(frequency)) {
      // For monthly: first period end is selected date/last day of April, start is 1st April or day after previous month's end
      let currentMonth = taxYearStart.getMonth();
      let currentYear = taxYearStart.getFullYear();
      // First period end
      if (periodEndDate === "last") {
        periodEnd = new Date(currentYear, currentMonth + 1, 0); // last day of April
      } else {
        periodEnd = new Date(
          currentYear,
          currentMonth,
          parseInt(periodEndDate, 10),
        );
        // Clamp if overflow
        if (periodEnd.getMonth() !== currentMonth) {
          periodEnd = new Date(currentYear, currentMonth + 1, 0);
        }
      }
      // First period start
      if (periodEndDate === "last") {
        periodStart = new Date(currentYear, currentMonth, 1);
      } else {
        // Start is day after previous month's selected end date
        let prevMonth = currentMonth - 1;
        let prevYear = currentYear;
        if (prevMonth < 0) {
          prevMonth = 11;
          prevYear--;
        }
        let prevEndDay =
          periodEndDate === "last"
            ? new Date(prevYear, prevMonth + 1, 0).getDate()
            : parseInt(periodEndDate, 10);
        periodStart = new Date(prevYear, prevMonth, prevEndDay + 1);
        // Clamp to 1st April if before tax year start
        if (periodStart < taxYearStart)
          periodStart = new Date(currentYear, currentMonth, 1);
      }
    } else {
      // Weekly types: find first selected weekday on/after tax year start
      periodEnd = new Date(taxYearStart);
      const targetDay = WEEKDAYS.indexOf(periodEndDay);
      while (periodEnd.getDay() !== targetDay) {
        periodEnd.setDate(periodEnd.getDate() + 1);
      }
    }

    // Assign periodStart for the first period before the loop
    if (["monthly", "quarterly", "yearly"].includes(frequency)) {
      if (periodEndDate === "last") {
        periodStart = new Date(
          taxYearStart.getFullYear(),
          taxYearStart.getMonth(),
          1,
        );
      } else {
        // Start is day after previous month's selected end date
        let currentMonth = taxYearStart.getMonth();
        let currentYear = taxYearStart.getFullYear();
        let prevMonth = currentMonth - 1;
        let prevYear = currentYear;
        if (prevMonth < 0) {
          prevMonth = 11;
          prevYear--;
        }
        let prevEndDay =
          periodEndDate === "last"
            ? new Date(prevYear, prevMonth + 1, 0).getDate()
            : parseInt(periodEndDate, 10);
        periodStart = new Date(prevYear, prevMonth, prevEndDay + 1);
        // Clamp to 1st April if before tax year start
        if (periodStart < taxYearStart)
          periodStart = new Date(currentYear, currentMonth, 1);
      }
    } else {
      periodStart = new Date(taxYearStart);
    }
    for (let i = 0; i < numPeriods; ++i) {
      // Calculate period start
      if (["monthly", "quarterly", "yearly"].includes(frequency)) {
        if (i === 0) {
          // Already set above
        } else {
          // Start is day after previous period's end
          periodStart = new Date(periods[i - 1].period_end);
          periodStart.setDate(periodStart.getDate() + 1);
        }
      } else {
        if (i === 0) {
          // Already set above
        } else {
          periodStart = new Date(periods[i - 1].period_end);
          periodStart.setDate(periodStart.getDate() + 1);
        }
      }
      // Calculate pay date using pay date rule
      let payDate = new Date(periodEnd);
      if (payDateRule && payDateRule.type) {
        switch (payDateRule.type) {
          case "PED":
            // Pay date is period end date
            payDate = new Date(periodEnd);
            break;
          case "weekday_on_after_PED":
          case "weekday_on_or_after_PED": {
            // Find first selected weekday on or after PED
            const targetDay = WEEKDAYS.indexOf(payDateRule.weekday);
            payDate = new Date(periodEnd);
            while (payDate.getDay() !== targetDay) {
              payDate.setDate(payDate.getDate() + 1);
            }
            break;
          }
          case "weekday_on_before_PED": {
            // Find last selected weekday on or before PED
            const targetDay = WEEKDAYS.indexOf(payDateRule.weekday);
            payDate = new Date(periodEnd);
            while (payDate.getDay() !== targetDay) {
              payDate.setDate(payDate.getDate() - 1);
            }
            break;
          }
          case "last_working_day": {
            // Last Mon-Fri of the month
            let d = new Date(periodEnd);
            while (d.getDay() === 0 || d.getDay() === 6) {
              d.setDate(d.getDate() - 1);
            }
            payDate = d;
            break;
          }
          case "last_day": {
            // Last calendar day of the month
            payDate = new Date(periodEnd);
            break;
          }
          case "particular_date_this_month": {
            // Particular date in same month as period end
            payDate = new Date(periodEnd);
            payDate.setDate(parseInt(payDateRule.date, 10));
            break;
          }
          case "particular_date_next_month": {
            // Particular date in next month
            payDate = new Date(periodEnd);
            payDate.setMonth(payDate.getMonth() + 1);
            payDate.setDate(parseInt(payDateRule.date, 10));
            break;
          }
          default:
            payDate = new Date(periodEnd);
        }
      }
      // For weekly, stop if pay date outside tax year
      if (frequency === "weekly" && payDate > taxYearEnd) break;
      // Clamp periodEnd if after taxYearEnd
      let thisPeriodEnd = new Date(periodEnd);
      if (thisPeriodEnd > taxYearEnd) thisPeriodEnd = new Date(taxYearEnd);
      periods.push({
        type: frequency,
        start_day: 0,
        end_day: 0,
        pay_date_rule: payDateRule,
        period_start: periodStart.toISOString().slice(0, 10),
        period_end: thisPeriodEnd.toISOString().slice(0, 10),
        pay_date: payDate.toISOString().slice(0, 10),
        tax_period_label: `${periodLabel} ${i + 1}`,
        active: true,
        notes: null,
        period_number: i + 1,
      });
      // Advance periodEnd for next period
      periodEnd = getNextPeriodEnd(periodEnd, i);
    }
    return periods;
  }

  // Submission handler
  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    try {
      // --- Validation for label field (now required, max 20 chars) ---
      if (!label || label.trim().length === 0) {
        setError("Schedule label is required.");
        setIsSubmitting(false);
        return;
      }
      if (label.length > 20) {
        setError("Schedule label must be at most 20 characters.");
        setIsSubmitting(false);
        return;
      }
      let end_day = 1;
      if (["weekly", "two_weekly", "four_weekly"].includes(safePayFrequency)) {
        end_day = WEEKDAYS.indexOf(periodEndDay);
      } else {
        end_day = periodEndDate === "last" ? 99 : parseInt(periodEndDate, 10);
      }
      const pay_date_rule = buildPayDateRule();
      // Validate single period for schema compliance
      const schema = z.object({
        label: z.string().min(1).max(20),
        type: z.string(),
        end_day: z.number(),
        pay_date_rule: z.any(),
      });
      schema.parse({ label, type: safePayFrequency, end_day, pay_date_rule });
      // Generate all periods for the tax year
      const periods = generatePayPeriodsForTaxYear({
        frequency: safePayFrequency,
        periodEndDay,
        periodEndDate,
        payDateRule: pay_date_rule,
      });
      // Create schedule and all periods in one mutation
      await createScheduleAndPeriodsMutation.mutateAsync({
        label,
        type: safePayFrequency,
        periods,
      });
      handleClose();
    } catch (err: any) {
      setError(err.message || "Validation error");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(val) => {
        if (!val) handleClose();
      }}
    >
      <DialogContent className="max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Setup Pay Period Schedule</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="mb-1 block">Schedule Name (optional)</label>
            <Input
              value={label}
              onChange={(e) => {
                setLabel(e.target.value);
                if (error) setError(null);
              }}
              placeholder="e.g. Weekly Payroll"
              maxLength={20}
            />
            <div className="text-muted-foreground flex justify-between text-xs">
              <span>Max 20 characters</span>
              <span>{label.length}/20</span>
            </div>
            {error && <div className="mt-1 text-xs text-red-600">{error}</div>}
          </div>
          <div>
            <label className="mb-1 block">
              Period End{" "}
              {["weekly", "two_weekly", "four_weekly"].includes(payFrequency)
                ? "Day"
                : "Date"}
            </label>
            {periodEndInput}
          </div>
          <div>
            <label className="mb-1 block">Pay Date Rule</label>
            <RadioGroup
              value={payDateRuleType}
              onValueChange={setPayDateRuleType}
              className="mb-2"
            >
              {payDateRuleOptions.map((opt) => (
                <div
                  key={opt.value}
                  className="mb-1 flex items-center space-x-2"
                >
                  <RadioGroupItem value={opt.value} id={opt.value} />
                  <label htmlFor={opt.value}>{opt.label}</label>
                  {/* Show extra selectors for some rules */}
                  {[
                    "weekday_on_before_PED",
                    "weekday_on_after_PED",
                    "weekday_after_PED",
                    "weekday_before_PED",
                  ].includes(opt.value) &&
                    payDateRuleType === opt.value && (
                      <Select
                        value={payDateWeekday}
                        onValueChange={setPayDateWeekday}
                      >
                        <SelectTrigger className="ml-2 w-32">
                          <SelectValue placeholder="Weekday" />
                        </SelectTrigger>
                        <SelectContent>
                          {WEEKDAYS.map((d) => (
                            <SelectItem key={d} value={d}>
                              {d}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  {[
                    "particular_date_this_month",
                    "particular_date_next_month",
                  ].includes(opt.value) &&
                    payDateRuleType === opt.value && (
                      <>
                        <Select
                          value={payDateParticularDate}
                          onValueChange={setPayDateParticularDate}
                        >
                          <SelectTrigger className="ml-2 w-20">
                            <SelectValue placeholder="Date" />
                          </SelectTrigger>
                          <SelectContent>
                            {PERIOD_DATES.map((d) => (
                              <SelectItem key={d} value={d}>
                                {d}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Select
                          value={payDateWhichMonth}
                          onValueChange={setPayDateWhichMonth}
                        >
                          <SelectTrigger className="ml-2 w-28">
                            <SelectValue placeholder="Month" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="this">This Month</SelectItem>
                            <SelectItem value="next">Next Month</SelectItem>
                          </SelectContent>
                        </Select>
                      </>
                    )}
                </div>
              ))}
            </RadioGroup>
          </div>
          {safePayFrequency &&
            ((["weekly", "two_weekly", "four_weekly"].includes(
              safePayFrequency,
            ) &&
              periodEndDay &&
              payDateRuleType) ||
              (["monthly", "quarterly", "yearly"].includes(safePayFrequency) &&
                periodEndDate &&
                payDateRuleType)) && (
              <div className="mt-4">
                <label className="mb-1 block font-semibold">
                  Schedule Preview
                </label>
                <div className="overflow-x-auto">
                  <table className="min-w-full border border-gray-300 text-sm">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border px-2 py-1">#</th>
                        <th className="border px-2 py-1">Start Date</th>
                        <th className="border px-2 py-1">End Date</th>
                        <th className="border px-2 py-1">Pay Date</th>
                        <th className="border px-2 py-1">Tax Period</th>
                      </tr>
                    </thead>
                    <tbody>
                      {generatePayPeriodsForTaxYear({
                        frequency: safePayFrequency,
                        periodEndDay,
                        periodEndDate,
                        payDateRule: buildPayDateRule(),
                      }).map((p, i) => (
                        <tr key={i} className="even:bg-gray-50">
                          <td className="border px-2 py-1">{i + 1}</td>
                          <td className="border px-2 py-1">
                            {formatDateToDDMMYYYY(p.period_start)}
                          </td>
                          <td className="border px-2 py-1">
                            {formatDateToDDMMYYYY(p.period_end)}
                          </td>
                          <td className="border px-2 py-1">
                            {formatDateToDDMMYYYY(p.pay_date)}
                          </td>
                          <td className="border px-2 py-1">
                            {p.tax_period_label}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          {error && <div className="text-sm text-red-600">{error}</div>}
          <DialogFooter>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? "Creating..."
                : `Create ${payFrequency.replace("_", " ")} Schedule`}
            </Button>
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
