import { ipcMain } from "electron";
import { getEmployerDb } from "../main/EmployerDbManager";
import * as schema from "../../drizzle/schema/employer/payPeriod";
import { eq } from "drizzle-orm";

// Fetch all pay periods
ipcMain.handle("employerDb:getPayPeriods", async (event, dbPath: string) => {
  console.log("[IPC] getPayPeriods called with dbPath:", dbPath);
  try {
    const entry = getEmployerDb(dbPath);
    if (!entry) {
      console.error(
        "[IPC] getPayPeriods: Employer DB is not open for dbPath:",
        dbPath,
      );
      return { success: false, error: "Employer DB is not open" };
    }
    const db = entry.db;
    const payPeriods = await db.select().from(schema.payPeriods).all();
    console.log(
      "[IPC] getPayPeriods: returning",
      payPeriods.length,
      "pay periods",
    );
    return { success: true, payPeriods };
  } catch (err: any) {
    console.error("[IPC] getPayPeriods error:", err);
    return { success: false, error: err.message };
  }
});

// Rename a pay period schedule (update label)
ipcMain.handle(
  "employerDb:renamePayPeriodSchedule",
  async (event, { dbPath, scheduleId, label }) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      if (
        !label ||
        typeof label !== "string" ||
        label.length === 0 ||
        label.length > 20
      ) {
        return {
          success: false,
          error: "Label is required and must be at most 20 characters.",
        };
      }
      if (!scheduleId || typeof scheduleId !== "string") {
        return { success: false, error: "ScheduleId is required." };
      }
      const now = Math.floor(Date.now() / 1000);
      const {
        payPeriodSchedules,
      } = require("../../drizzle/schema/employer/payPeriodSchedule");
      const result = await db
        .update(payPeriodSchedules)
        .set({ label, updated_at: now })
        .where(require("drizzle-orm").eq(payPeriodSchedules.id, scheduleId));
      return { success: true };
    } catch (err) {
      console.error("[IPC] renamePayPeriodSchedule error:", err);
      return {
        success: false,
        error:
          err && typeof err === "object" && "message" in err
            ? (err as any).message
            : String(err),
      };
    }
  },
);

// Add a pay period with new schedule (always create new schedule, label max 20 chars)
ipcMain.handle(
  "employerDb:addPayPeriod",
  async (event, dbPath: string, data: any) => {
    console.log("[DEBUG] employerDb:addPayPeriod payload:", data);
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      // Validate label
      if (
        !data.label ||
        typeof data.label !== "string" ||
        data.label.length === 0 ||
        data.label.length > 20
      ) {
        return {
          success: false,
          error: "Label is required and must be at most 20 characters.",
        };
      }
      // Validate type
      if (!data.type || typeof data.type !== "string") {
        return { success: false, error: "Type is required." };
      }
      // Create new schedule
      const scheduleId = crypto.randomUUID();
      const now = Math.floor(Date.now() / 1000);
      const [schedule] = await db
        .insert(
          require("../../drizzle/schema/employer/payPeriodSchedule")
            .payPeriodSchedules,
        )
        .values({
          id: scheduleId,
          label: data.label,
          type: data.type,
          created_at: now,
          updated_at: now,
        })
        .returning();
      // Insert pay period with new schedule_id
      const payPeriodId = crypto.randomUUID();
      const [payPeriod] = await db
        .insert(schema.payPeriods)
        .values({
          ...data,
          id: payPeriodId,
          schedule_id: scheduleId,
          created_at: now,
          updated_at: now,
        })
        .returning();
      return { success: true, schedule, payPeriod };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Fetch all pay period schedules
ipcMain.handle(
  "employerDb:getPayPeriodSchedules",
  async (event, dbPath: string) => {
    try {
      console.log("[IPC] getPayPeriodSchedules called with dbPath:", dbPath);
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      // Import the payPeriodSchedules table from the correct schema file
      const {
        payPeriodSchedules,
      } = require("../../drizzle/schema/employer/payPeriodSchedule");
      const schedules = await db.select().from(payPeriodSchedules).all();
      return { success: true, schedules };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Delete a pay period schedule by id
ipcMain.handle(
  "employerDb:deletePayPeriodSchedule",
  async (event, { dbPath, scheduleId }) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const {
        payPeriodSchedules,
      } = require("../../drizzle/schema/employer/payPeriodSchedule");
      const deleted = await db
        .delete(payPeriodSchedules)
        .where(require("drizzle-orm").eq(payPeriodSchedules.id, scheduleId));
      return { success: true, deletedCount: deleted.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Add schedule and all pay periods for it (batch)
ipcMain.handle(
  "employerDb:addPayPeriodScheduleAndPeriods",
  async (
    event,
    dbPath: string,
    payload: { label: string; type: string; periods: any[] },
  ) => {
    console.log(
      "[DEBUG] employerDb:addPayPeriodScheduleAndPeriods payload:",
      payload,
    );
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      // Validate label
      if (
        !payload.label ||
        typeof payload.label !== "string" ||
        payload.label.length === 0 ||
        payload.label.length > 20
      ) {
        return {
          success: false,
          error: "Label is required and must be at most 20 characters.",
        };
      }
      // Validate type
      if (!payload.type || typeof payload.type !== "string") {
        return { success: false, error: "Type is required." };
      }
      // Create new schedule
      const scheduleId = crypto.randomUUID();
      const now = Math.floor(Date.now() / 1000);
      const [schedule] = await db
        .insert(
          require("../../drizzle/schema/employer/payPeriodSchedule")
            .payPeriodSchedules,
        )
        .values({
          id: scheduleId,
          label: payload.label,
          type: payload.type,
          created_at: now,
          updated_at: now,
        })
        .returning();
      // Insert all periods with new schedule_id
      // Validate all periods have period_number and period_end
      for (const period of payload.periods) {
        if (
          typeof period.period_number !== "number" ||
          typeof period.period_end !== "string" ||
          !period.period_end.match(/^\d{4}-\d{2}-\d{2}$/)
        ) {
          return {
            success: false,
            error:
              "Each pay period must include a valid period_number (integer) and period_end (YYYY-MM-DD string).",
          };
        }
      }
      const periodsToInsert = payload.periods.map((period) => ({
        ...period,
        id: crypto.randomUUID(),
        schedule_id: scheduleId,
        created_at: now,
        updated_at: now,
      }));
      const insertedPeriods = await db
        .insert(schema.payPeriods)
        .values(periodsToInsert)
        .returning();
      return { success: true, schedule, payPeriods: insertedPeriods };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Add multiple pay periods (batch insert)
ipcMain.handle(
  "employerDb:addPayPeriods",
  async (event, dbPath: string, payPeriods: any[]) => {
    console.log("[DEBUG] employerDb:addPayPeriods payload:", payPeriods);
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const inserted = await db
        .insert(schema.payPeriods)
        .values(payPeriods)
        .returning();
      return { success: true, payPeriods: inserted };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Update a pay period
ipcMain.handle(
  "employerDb:updatePayPeriod",
  async (event, dbPath: string, payPeriod: any) => {
    console.log("[DEBUG] employerDb:updatePayPeriod payload:", payPeriod);
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const [updated] = await db
        .update(schema.payPeriods)
        .set(payPeriod)
        .where(eq(schema.payPeriods.id, payPeriod.id))
        .returning();
      return { success: true, payPeriod: updated };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Delete a pay period
ipcMain.handle(
  "employerDb:deletePayPeriod",
  async (event, dbPath: string, id: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      await db.delete(schema.payPeriods).where(eq(schema.payPeriods.id, id));
      return { success: true, id };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

import { PAY_PERIOD_TYPES } from "../../drizzle/schema/employer/payPeriod";
import { and } from "drizzle-orm";

// Type guard for pay period type
function isPayPeriodType(
  type: string,
): type is (typeof PAY_PERIOD_TYPES)[number] {
  return (PAY_PERIOD_TYPES as readonly string[]).includes(type);
}

// Delete all pay periods by type and scheduleId
ipcMain.handle(
  "employerDb:deletePayPeriodsByTypeAndName",
  async (event, dbPath: string, type: string, scheduleId: string | null) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      // Import the payPeriodSchedules table from the correct schema file
      const {
        payPeriodSchedules,
      } = require("../../drizzle/schema/employer/payPeriodSchedule");
      // Validate and narrow type
      if (!isPayPeriodType(type)) {
        return { success: false, error: `Invalid period type: ${type}` };
      }
      const periodType = type;
      let whereClause;
      if (scheduleId !== null) {
        whereClause = and(
          eq(schema.payPeriods.type, periodType),
          eq(schema.payPeriods.schedule_id, scheduleId),
        );
      } else {
        whereClause = eq(schema.payPeriods.type, periodType);
      }
      // Begin transaction
      await db.transaction(async (tx: any) => {
        // Delete pay periods
        await tx.delete(schema.payPeriods).where(whereClause);
        // If scheduleId is provided, delete the schedule as well
        if (scheduleId !== null) {
          console.log(
            `[DEBUG] Attempting to delete schedule with id: ${scheduleId}`,
          );
          const scheduleDeleteResult = await tx
            .delete(payPeriodSchedules)
            .where(eq(payPeriodSchedules.id, scheduleId));
          console.log(`[DEBUG] Schedule delete result:`, scheduleDeleteResult);
        }
      });
      // No need to return deletedCount as multiple tables are affected
      return { success: true };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);
