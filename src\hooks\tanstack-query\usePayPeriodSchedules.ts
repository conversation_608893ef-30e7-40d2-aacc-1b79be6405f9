import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEmployerDBContext } from '@/providers/employer-db-provider';
import type { PayPeriodSchedule } from '@/drizzle/schema/employer/payPeriodSchedule';

// IPC or service call to get all pay period schedules
async function fetchPayPeriodSchedules(dbPath: string): Promise<PayPeriodSchedule[]> {
  if (!dbPath) throw new Error('No employer database path');
  // Replace with actual IPC/service logic
  const result = await window.api.invoke('employerDb:getPayPeriodSchedules', dbPath);
  if (result && result.success) return result.schedules;
  throw new Error(result?.error || 'Failed to fetch pay period schedules');
}

export function usePayPeriodSchedules() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(e => e.id === ctx.activeEmployerId)?.dbPath;
  return useQuery({
    queryKey: ['payPeriodSchedules', dbPath],
    queryFn: () => dbPath ? fetchPayPeriodSchedules(dbPath) : Promise.resolve([]),
    enabled: !!dbPath,
  });
}

// --- Mutation for renaming a pay period schedule ---
async function renamePayPeriodSchedule({ dbPath, scheduleId, label }: { dbPath: string; scheduleId: string; label: string }) {
  if (!dbPath) throw new Error('No employer database path');
  if (!scheduleId) throw new Error('No scheduleId provided');
  if (!label || label.trim() === "") throw new Error('No label provided');
  const result = await window.api.invoke('employerDb:renamePayPeriodSchedule', { dbPath, scheduleId, label });
  if (result && result.success) return true;
  throw new Error(result?.error || 'Failed to rename pay period schedule');
}

export function useRenamePayPeriodScheduleMutation() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(e => e.id === ctx.activeEmployerId)?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ scheduleId, label }: { scheduleId: string; label: string }) => {
      if (!dbPath) throw new Error('No employer database path');
      return renamePayPeriodSchedule({ dbPath, scheduleId, label });
    },
    onSuccess: () => {
      if (dbPath) {
        queryClient.invalidateQueries({ queryKey: ['payPeriodSchedules', dbPath] });
        queryClient.invalidateQueries({ queryKey: ['payPeriods', dbPath] });
        queryClient.invalidateQueries({ queryKey: ['payFrequencies', dbPath] });
      }
    },
  });
}

// --- Mutation for deleting a pay period schedule ---
async function deletePayPeriodSchedule({ dbPath, scheduleId }: { dbPath: string; scheduleId: string }) {
  if (!dbPath) throw new Error('No employer database path');
  if (!scheduleId) throw new Error('No scheduleId provided');
  const result = await window.api.invoke('employerDb:deletePayPeriodSchedule', { dbPath, scheduleId });
  if (result && result.success) return true;
  throw new Error(result?.error || 'Failed to delete pay period schedule');
}

export function useDeletePayPeriodScheduleMutation() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(e => e.id === ctx.activeEmployerId)?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (scheduleId: string) => {
      if (!dbPath) throw new Error('No employer database path');
      return deletePayPeriodSchedule({ dbPath, scheduleId });
    },
    onSuccess: () => {
      if (dbPath) {
        queryClient.invalidateQueries({ queryKey: ['payPeriodSchedules', dbPath] });
      }
    },
  });
}
