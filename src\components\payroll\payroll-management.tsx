"use client";

import { EMPLOYER_DB_LOCALSTORAGE_PREFIX } from "@/constants/file";

import React, { useState, useEffect, useRef } from "react";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import { useQueryClient } from "@tanstack/react-query";
import { ActionToolbar } from "@/components/layouts/action-toolbar";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Save, Settings } from "lucide-react";

// Import payroll components
import PayPeriodSelector from "@/components/payroll/pay-period-selector";
import { usePayPeriodSchedules } from "@/hooks/tanstack-query/usePayPeriodSchedules";
import { normalizeType } from "./pay-period-selector";
import { PAY_PERIOD_TYPES } from "@/drizzle/schema/employer/payPeriod";
import { PayrollWizardModal } from "@/components/payroll/pay-periods/PayrollWizardModal";
import { usePayPeriodsQuery } from "@/hooks/tanstack-query/usePayPeriods";
import { usePayFrequenciesQuery } from "@/hooks/tanstack-query/usePayFrequencies";
import PayrollOverview from "@/components/payroll/payroll-overview";
import PayslipEditor from "@/components/payroll/payslip-editor";
import BatchEditor from "@/components/payroll/batch-editor";

// Define the type for our persisted state
type PayrollState = {
  activeView: "overview" | "payslip" | "batch";
  activePeriod: string;
  selectedEmployee: string | null;
  // Overview filters and settings
  overviewStatusFilter: "all" | "open" | "closed";
  overviewSearchTerm: string;
  overviewSorting: { id: string; desc: boolean }[];
  // Batch edit filters and settings
  batchStatusFilter: "all" | "open" | "closed";
  batchSelectedColumns: string[];
  batchSorting: { id: string; desc: boolean }[];
};

// Storage key for localStorage
const PAYROLL_STATE_KEY = `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_payroll_state`;

type PayPeriodType =
  | "weekly"
  | "two_weekly"
  | "four_weekly"
  | "monthly"
  | "quarterly"
  | "yearly";

const DB_TO_ENUM: Record<string, PayPeriodType> = {
  Weekly: "weekly",
  "2-Weekly": "two_weekly",
  "4-Weekly": "four_weekly",
  Monthly: "monthly",
  Quarterly: "quarterly",
  Yearly: "yearly",
};

const ENUM_TO_LABEL: Record<PayPeriodType, string> = {
  weekly: "weekly",
  two_weekly: "2-weekly",
  four_weekly: "4-weekly",
  monthly: "monthly",
  quarterly: "quarterly",
  yearly: "yearly",
};

type PayPeriodSetupPromptProps = {
  onSetup: (freq: PayPeriodType) => void;
  payPeriods: any[];
};

const PayPeriodSetupPrompt: React.FC<PayPeriodSetupPromptProps> = ({
  onSetup,
  payPeriods,
}) => {
  const { data: frequencies, isLoading, error } = usePayFrequenciesQuery();

  const mappedFrequencies = frequencies?.map(
    (dbFreq) => DB_TO_ENUM[dbFreq as keyof typeof DB_TO_ENUM],
  );

  if (isLoading)
    return (
      <div className="text-muted-foreground w-full px-2 py-4 text-center">
        Loading pay frequencies...
      </div>
    );
  if (error)
    return (
      <div className="w-full px-2 py-4 text-center text-red-500">
        Error loading pay frequencies
      </div>
    );
  if (!frequencies || frequencies.length === 0) {
    return (
      <div className="text-muted-foreground w-full px-2 py-4 text-center">
        No employees found or pay frequencies not set
      </div>
    );
  }
  // Find missing frequencies
  const setupFrequencies: PayPeriodType[] = frequencies
    .map((dbFreq) => DB_TO_ENUM[dbFreq as keyof typeof DB_TO_ENUM])
    .filter((enumFreq): enumFreq is PayPeriodType => !!enumFreq)
    .filter(
      (enumFreq) =>
        !payPeriods?.some((p) => p.type?.toLowerCase() === enumFreq),
    );
  if (setupFrequencies.length === 0) return null;
  return (
    <div className="flex w-full flex-col items-center gap-4 px-2 py-4 text-center">
      <span className="text-muted-foreground mb-2">
        Not all pay periods are set up yet.
      </span>
      {setupFrequencies.length === 0
        ? null
        : setupFrequencies.map((enumFreq) => (
            <Button
              key={enumFreq}
              variant="default"
              size="lg"
              onClick={() => onSetup(enumFreq)}
              className="w-fit"
            >
              {`Set up ${ENUM_TO_LABEL[enumFreq]} pay periods`}
            </Button>
          ))}
    </div>
  );
};

const PayrollManagement: React.FC = () => {
  // ...existing code...
  // At the point where you render PayPeriodSelector:
  // (Replace the old call with this)

  // Always call hooks at the top level
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const { data: schedules = [] } = usePayPeriodSchedules();
  const scheduleLabels = React.useMemo(() => {
    if (!Array.isArray(schedules)) return {};
    return (
      schedules as Array<{ id: string; label: string; type?: string }>
    ).reduce<Record<string, string>>((map, sched) => {
      map[sched.id] =
        sched.label && sched.label.trim() !== ""
          ? sched.label
          : sched.type
            ? normalizeType(sched.type)
            : "";
      return map;
    }, {});
  }, [schedules]);
  const queryClient = useQueryClient();

  // State for pay period setup wizard
  const [wizardOpen, setWizardOpen] = useState(false);
  const [pendingFrequency, setPendingFrequency] = useState<
    (typeof PAY_PERIOD_TYPES)[number] | null
  >("monthly");
  const [wizardDismissed, setWizardDismissed] = useState(false);
  // Initialize state with values from localStorage or defaults
  const [stateInitialized, setStateInitialized] = useState(false);
  const [activeView, setActiveView] = useState<
    "overview" | "payslip" | "batch"
  >("overview");
  const [activePeriod, setActivePeriod] = useState<string>("march-2025");
  const [selectedEmployee, setSelectedEmployee] = useState<string | null>(null);
  const [isPeriodSelectorVisible, setIsPeriodSelectorVisible] = useState(true);
  const {
    data: payPeriods = [],
    isLoading: payPeriodsLoading,
    error: payPeriodsError,
  } = usePayPeriodsQuery();

  // Overview state
  const [overviewStatusFilter, setOverviewStatusFilter] = useState<
    "all" | "open" | "closed"
  >("all");
  const [overviewSearchTerm, setOverviewSearchTerm] = useState<string>("");
  const [overviewSorting, setOverviewSorting] = useState<
    { id: string; desc: boolean }[]
  >([{ id: "name", desc: false }]);

  // Batch edit state
  const [batchStatusFilter, setBatchStatusFilter] = useState<
    // ...other component code...

    // Render PayPeriodSelector after all variables are initialized

    "all" | "open" | "closed"
  >("all");
  const [batchSelectedColumns, setBatchSelectedColumns] = useState<string[]>([
    "salary",
    "hourlyRate",
    "hours",
    "bonus",
    "deduction",
  ]);
  const [batchSorting, setBatchSorting] = useState<
    { id: string; desc: boolean }[]
  >([{ id: "name", desc: false }]);

  // Reference to the Tabs component to programmatically change tabs
  const tabsRef = useRef<HTMLDivElement>(null);

  // Load saved state from localStorage on component mount
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(PAYROLL_STATE_KEY);
      if (savedState) {
        const parsedState = JSON.parse(savedState) as PayrollState;

        // Load basic view state
        setActiveView(parsedState.activeView);
        setActivePeriod(parsedState.activePeriod);
        setSelectedEmployee(parsedState.selectedEmployee);

        // Load overview filters and settings
        if (parsedState.overviewStatusFilter) {
          setOverviewStatusFilter(parsedState.overviewStatusFilter);
        }
        if (parsedState.overviewSearchTerm) {
          setOverviewSearchTerm(parsedState.overviewSearchTerm);
        }
        if (parsedState.overviewSorting) {
          setOverviewSorting(parsedState.overviewSorting);
        }

        // Load batch edit filters and settings
        if (parsedState.batchStatusFilter) {
          setBatchStatusFilter(parsedState.batchStatusFilter);
        }
        if (parsedState.batchSelectedColumns) {
          setBatchSelectedColumns(parsedState.batchSelectedColumns);
        }
        if (parsedState.batchSorting) {
          setBatchSorting(parsedState.batchSorting);
        }
      }
    } catch (error) {
      console.error("Error loading payroll state from localStorage:", error);
    } finally {
      setStateInitialized(true);
    }
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    // Only save after initial state is loaded to prevent overwriting with defaults
    if (stateInitialized) {
      const stateToSave: PayrollState = {
        activeView,
        activePeriod,
        selectedEmployee,
        // Overview filters and settings
        overviewStatusFilter,
        overviewSearchTerm,
        overviewSorting,
        // Batch edit filters and settings
        batchStatusFilter,
        batchSelectedColumns,
        batchSorting,
      };
      try {
        localStorage.setItem(PAYROLL_STATE_KEY, JSON.stringify(stateToSave));
      } catch (error) {
        console.error("Error saving payroll state to localStorage:", error);
      }
    }
  }, [
    activeView,
    activePeriod,
    selectedEmployee,
    overviewStatusFilter,
    overviewSearchTerm,
    overviewSorting,
    batchStatusFilter,
    batchSelectedColumns,
    batchSorting,
    stateInitialized,
  ]);

  // Handle employee selection for payslip view
  const handleEmployeeSelect = (employeeId: string) => {
    console.log("Employee selected:", employeeId);
    setSelectedEmployee(employeeId);
    setActiveView("payslip");
  };

  // Handle switching to batch edit view
  const handleSwitchToBatch = () => {
    console.log("Switching to batch edit");
    setActiveView("batch");
  };

  // Handle switching to overview
  const handleSwitchToOverview = () => {
    console.log("Switching to overview");
    setActiveView("overview");
  };

  // Effect to update the UI when activeView changes
  useEffect(() => {
    console.log("Active view changed to:", activeView);
  }, [activeView]);

  // Listen for period selector visibility changes
  useEffect(() => {
    // Check initial visibility state
    const initialVisibility =
      localStorage.getItem(
        `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_visible`,
      ) === "true";
    setIsPeriodSelectorVisible(initialVisibility !== false); // Default to true if not set

    // Listen for visibility change events
    const handleVisibilityChange = (
      event: CustomEvent<{ visible: boolean }>,
    ) => {
      setIsPeriodSelectorVisible(event.detail.visible);
    };

    // Add event listener
    window.addEventListener(
      "periodSelectorVisibilityChange",
      handleVisibilityChange as EventListener,
    );

    // Clean up
    return () => {
      window.removeEventListener(
        "periodSelectorVisibilityChange",
        handleVisibilityChange as EventListener,
      );
    };
  }, []);

  // Don't render until state is initialized to prevent flicker
  console.log("[PayrollManagement] stateInitialized:", stateInitialized);
  if (!stateInitialized) {
    return <div className="w-full px-2">Loading...</div>;
  }

  // Helper: handle wizard close and refetch pay periods
  const handleWizardClose = (periodCreated?: boolean) => {
    // Invalidate both payPeriods and payFrequencies queries for the active dbPath
    if (dbPath) {
      queryClient.invalidateQueries({ queryKey: ["payPeriods", dbPath] });
      queryClient.invalidateQueries({ queryKey: ["payFrequencies", dbPath] });
    } else {
      // fallback: invalidate all if dbPath missing
      queryClient.invalidateQueries({ queryKey: ["payPeriods"] });
      queryClient.invalidateQueries({ queryKey: ["payFrequencies"] });
    }
    setWizardOpen(false);
    setWizardDismissed(true);
    setPendingFrequency(null);
    // No additional invalidation needed here; already handled above
  };

  return (
    <>
      {/* Payroll Wizard Modal (auto and manual) */}
      {pendingFrequency && (
        <PayrollWizardModal
          open={wizardOpen && !wizardDismissed}
          payFrequency={pendingFrequency}
          onClose={() => handleWizardClose(true)}
        />
      )}
      {/* Main Content */}
      <Tabs
        value={activeView}
        defaultValue="overview"
        className="flex min-h-0 w-full flex-1 flex-col"
        onValueChange={(value) => setActiveView(value as any)}
      >
        {/* Pay Period Selection with proper height transition */}
        <div className={`overflow-hidden transition-all duration-300`}>
          {(() => {
            console.log(
              "[PayrollManagement] payPeriodsLoading:",
              payPeriodsLoading,
            );
            console.log(
              "[PayrollManagement] payPeriodsError:",
              payPeriodsError,
            );
            console.log("[PayrollManagement] payPeriods:", payPeriods);
            console.log("[PayrollManagement] scheduleLabels:", scheduleLabels);
            return null;
          })()}
          {/* Always show the setup prompt above the selector if there are missing periods */}
          <PayPeriodSetupPrompt
            payPeriods={payPeriods || []}
            onSetup={(freq) => {
              setWizardDismissed(false);
              setWizardOpen(true);
              setPendingFrequency(freq);
            }}
          />
          {/* Show the selector if there are pay periods and scheduleLabels */}
          {payPeriods &&
            payPeriods.length > 0 &&
            Object.keys(scheduleLabels).length > 0 && (
              <PayPeriodSelector
                payPeriods={payPeriods}
                activePeriod={activePeriod}
                onPeriodChange={setActivePeriod}
                scheduleLabels={scheduleLabels}
                schedules={schedules}
              />
            )}
        </div>

        {/* Content container */}
        <div className="flex min-h-0 w-full flex-1 flex-col">
          <TabsContent
            value="overview"
            className="flex min-h-0 flex-1 flex-col"
          >
            {(() => {
              console.log("[PayrollManagement] Rendering PayrollOverview");
              return null;
            })()}
            <PayrollOverview
              periodId={activePeriod}
              onEmployeeSelect={handleEmployeeSelect}
              onSwitchToBatch={handleSwitchToBatch}
              onSwitchToOverview={handleSwitchToOverview}
              activeView={activeView}
              initialStatusFilter={overviewStatusFilter}
              onStatusFilterChange={setOverviewStatusFilter}
              initialSearchTerm={overviewSearchTerm}
              onSearchTermChange={setOverviewSearchTerm}
              initialSorting={overviewSorting}
              handleSortingChange={setOverviewSorting}
            />
          </TabsContent>

          <TabsContent value="payslip" className="flex min-h-0 flex-1 flex-col">
            {(() => {
              console.log("[PayrollManagement] Rendering PayslipEditor");
              return null;
            })()}
            <PayslipEditor
              periodId={activePeriod}
              employeeId={selectedEmployee}
              onBackToOverview={handleSwitchToOverview}
              onSwitchToBatch={handleSwitchToBatch}
              payPeriods={payPeriods || []}
            />
          </TabsContent>

          <TabsContent value="batch" className="flex min-h-0 flex-1 flex-col">
            {(() => {
              console.log("[PayrollManagement] Rendering BatchEditor");
              return null;
            })()}
            <BatchEditor
              periodId={activePeriod}
              activeView={activeView}
              onSwitchToOverview={handleSwitchToOverview}
              onSwitchToBatch={handleSwitchToBatch}
              onEmployeeSelect={handleEmployeeSelect}
              initialStatusFilter={batchStatusFilter}
              onStatusFilterChange={setBatchStatusFilter}
              initialSelectedColumns={batchSelectedColumns}
              onSelectedColumnsChange={setBatchSelectedColumns}
              initialSorting={batchSorting}
              onSortingChange={setBatchSorting}
            />
          </TabsContent>
        </div>
      </Tabs>
    </>
  );
};

export default PayrollManagement;
