import { sqliteTable, text, integer, primaryKey, index, uniqueIndex } from 'drizzle-orm/sqlite-core';
import { z } from 'zod';
import { sql } from 'drizzle-orm';

// Enum for pay period types
export const PAY_PERIOD_TYPES = [
  'weekly',
  'two_weekly',
  'four_weekly',
  'monthly',
  'quarterly',
  'yearly',
] as const;

export const payPeriodTypeEnum = z.enum(PAY_PERIOD_TYPES);

export const payPeriods = sqliteTable(
  'pay_periods',
  {
    id: text('id').primaryKey().notNull(), // UUID
    schedule_id: text('schedule_id').notNull().references(() => require('./payPeriodSchedule').payPeriodSchedules.id), // FK to pay_period_schedules
    type: text('type', { enum: PAY_PERIOD_TYPES }).notNull(),
    start_day: integer('start_day').notNull(), // e.g. 1–31 or 0–6
    end_day: integer('end_day').notNull(),
    pay_date_rule: text('pay_date_rule', { mode: 'json' }).notNull(), // JSON
    period_number: integer('period_number').notNull(), // <--- NEW
    period_end: text('period_end').notNull(),         // <--- NEW, ISO date string
    active: integer('active', { mode: 'boolean' }).notNull().default(true),
    created_at: integer('created_at').notNull().default(sql`(strftime('%s','now'))`),
    updated_at: integer('updated_at').notNull().default(sql`(strftime('%s','now'))`),
    notes: text('notes'),
  },
  (table) => ({
    typeIdx: index('pay_periods_type_idx').on(table.type),
    activeIdx: index('pay_periods_active_idx').on(table.active),
    createdAtIdx: index('pay_periods_created_at_idx').on(table.created_at),
    updatedAtIdx: index('pay_periods_updated_at_idx').on(table.updated_at),

  })
);

export const payPeriodSchema = z.object({
  id: z.string().uuid(),
  schedule_id: z.string(),
  type: payPeriodTypeEnum,
  start_day: z.number().int(),
  end_day: z.number().int(),
  pay_date_rule: z.any(), // Consider a more specific Zod schema for pay date rules
  period_number: z.number().int(),    // <--- NEW
  period_end: z.string(),             // <--- NEW, ISO date string
  active: z.boolean(),
  created_at: z.number().int(),
  updated_at: z.number().int(),
  notes: z.string().nullable(),
});

export type PayPeriod = z.infer<typeof payPeriodSchema>;
